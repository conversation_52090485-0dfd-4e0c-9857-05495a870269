# 舆情监控系统 - augmentcode 执行方案

## 执行策略概述

使用 augmentcode 实现**前后端不分离**的全栈舆情监控系统，后端设置功能在前端web页面中直接展示和管理，采用传统的服务端渲染架构。

## Phase 1: 项目基础架构搭建

### augmentcode 执行命令序列：

1. **初始化全栈项目结构**
```
创建一个前后端不分离的Node.js全栈舆情监控系统：
- 后端：Express服务器、MongoDB数据模型、服务端渲染
- 前端：EJS模板引擎、Bootstrap UI、ECharts图表、原生JavaScript
- 一体化架构：所有功能在同一个应用中，包括数据展示和后端管理
- 配置：环境变量、数据库连接、依赖管理
请生成完整的package.json和全栈项目目录结构
```

2. **数据库模型设计**
```
为舆情监控系统设计MongoDB数据模型：
1. Article模型：文章数据（标题、内容、来源、时间、哈希去重）
2. Sentiment模型：情感分析结果（正面/负面/中性、关键词、地域）
3. Alert模型：预警记录（级别、类型、状态）
4. Settings模型：系统配置
包含索引优化和数据过期策略（12个月）
```

## Phase 2: 数据采集系统

### augmentcode 执行命令：

3. **信息源配置管理**
```
基于提供的80+信息源URL列表，创建信息源管理系统：
- 解析信息源配置文件
- 实现主备切换逻辑
- 支持动态添加/删除信息源
- 包含状态监控和错误统计
```

4. **通用爬虫服务**
```
开发通用的网页数据采集服务：
- 使用axios进行HTTP请求
- 支持多种数据格式（JSON/HTML/XML）
- 实现请求重试和超时处理
- 添加用户代理轮换和请求频率控制
- 包含数据清洗和格式化
```

5. **数据去重和存储**
```
实现智能去重和数据存储系统：
- 基于标题+内容的MD5哈希去重
- 批量数据插入优化
- 异常数据处理和日志记录
- 定时清理过期数据（12个月周期）
```

## Phase 3: 定时任务调度

### augmentcode 执行命令：

6. **任务调度系统**
```
使用node-cron创建定时任务系统：
- 30分钟一次的数据采集任务
- 每小时的情感分析任务
- 每日的数据统计和清理任务
- 任务状态监控和错误恢复
- 支持手动触发和任务暂停
```

## Phase 4: AI情感分析集成

### augmentcode 执行命令：

7. **DeepSeek AI集成**
```
集成DeepSeek API进行情感分析：
- 使用提供的API Key和调用方法
- 设计情感分析提示词模板
- 实现批量文本分析（避免API限制）
- 结果解析和结构化存储
- 包含关键词提取和地域识别
```

8. **情感分析优化**
```
优化情感分析的准确性和效率：
- 文本预处理（去除HTML标签、特殊字符）
- 分段处理长文本
- 结果缓存和重复分析避免
- 分析结果的置信度评估
- 异常结果的人工审核标记
```

## Phase 5: 可视化大屏开发

### augmentcode 执行命令：

9. **全栈管理界面开发**
```
创建集成数据展示和后端管理的全栈界面：
- 主仪表板：实时数据展示（文章统计、情感分布、热词云图、地域分布）
- 系统设置页面：信息源管理、采集参数配置、预警规则设置
- 数据管理页面：文章列表查看、情感分析结果、历史数据查询
- 监控管理页面：系统状态、任务监控、日志查看
- 预警管理页面：预警规则配置、历史预警记录、处理状态跟踪
- 使用EJS模板渲染，Bootstrap样式，ECharts图表
```

10. **前端交互功能实现**
```
实现前端页面的交互和管理功能：
- 表单提交和数据验证（信息源配置、预警规则）
- 实时数据更新和图表刷新
- 模态框弹窗进行数据编辑
- 分页和搜索功能
- 导入导出功能（Excel/CSV）
- 响应式设计适配移动端
- 前端路由和页面切换
```

## Phase 6: 预警系统开发

### augmentcode 执行命令：

11. **实时通信和数据同步**
```
实现WebSocket实时数据推送和页面同步：
- Socket.io客户端和服务端配置
- 实时图表数据更新
- 新预警消息推送
- 配置更改实时同步
- 系统状态实时监控
- 连接状态监控和自动重连
```

12. **集成预警和配置管理**
```
在web界面中集成预警系统和配置管理：
- 预警规则的可视化配置界面
- 实时预警消息展示和处理
- 系统参数在线调整
- 信息源状态监控和管理
- 任务调度的启停控制
- 配置备份和恢复功能
```

## Phase 7: 系统配置和管理

### augmentcode 执行命令：

13. **统一后台管理系统**
```
开发集成所有管理功能的后台系统：
- 用户权限管理（管理员/操作员/查看者）
- 系统配置一站式管理界面
- 数据库管理工具（备份、恢复、清理）
- API接口管理和测试工具
- 系统日志查看和分析
- 性能监控和告警设置
- 所有功能通过web界面操作，无需直接操作后端
```

14. **日志和监控系统**
```
实现系统监控和日志管理：
- 应用程序日志记录
- 性能指标收集（响应时间、内存使用）
- 错误跟踪和报告
- 系统健康检查API
- 日志文件轮转和清理
```

## Phase 8: 性能优化和部署

### augmentcode 执行命令：

15. **性能优化**
```
优化系统性能和稳定性：
- 数据库查询优化和索引调整
- 内存使用优化和垃圾回收
- API响应缓存策略
- 并发处理和队列机制
- 资源使用监控和告警
```

16. **生产部署配置**
```
准备生产环境部署配置：
- PM2进程管理配置
- Nginx反向代理设置
- SSL证书配置
- 环境变量和安全配置
- 数据库备份策略
- 健康检查和自动重启
```

## 执行时间安排

- **Phase 1-2**: 第1-2天（全栈项目基础和数据采集）
- **Phase 3-4**: 第3-4天（定时任务和AI分析）
- **Phase 5**: 第5-6天（集成管理界面开发）
- **Phase 6**: 第7-8天（预警系统和实时通信）
- **Phase 7-8**: 第9-10天（统一后台和部署优化）

## 全栈架构特点说明

### 1. 前后端不分离设计
- **服务端渲染**：使用EJS模板引擎，所有页面由服务器渲染后返回
- **一体化应用**：数据展示、系统管理、配置设置都在同一个应用中
- **统一入口**：通过web浏览器访问所有功能，无需单独的管理工具

### 2. 后端设置前端化
- **可视化配置**：所有后端参数通过web界面进行设置
- **实时生效**：配置更改立即生效，无需重启服务
- **权限控制**：不同用户角色看到不同的管理功能
- **操作日志**：记录所有通过web界面进行的配置操作

### 3. 功能集成展示
- **仪表板**：数据展示 + 系统状态监控
- **数据管理**：查看数据 + 数据操作（删除、导出）
- **系统设置**：参数配置 + 实时预览效果
- **任务管理**：查看任务状态 + 手动触发/停止任务
- **预警中心**：预警展示 + 规则配置 + 处理记录

## 质量保证

- 每个阶段完成后进行功能测试
- 关键模块要求生成单元测试代码
- 性能基准测试和压力测试
- 安全性检查和漏洞扫描
- 代码质量审查和优化建议